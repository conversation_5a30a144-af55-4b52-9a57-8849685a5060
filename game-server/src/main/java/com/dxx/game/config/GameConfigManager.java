package com.dxx.game.config;

import com.dxx.game.common.config.game.GameConfigLoader;
import com.dxx.game.common.config.game.GameConfiguration;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.config.Bean.Equip_updateLevel;
import com.dxx.game.config.object.MainConfig;
import com.dxx.game.dto.CommonProto;
import com.dxx.game.modules.common.support.CommonHelper;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Component("gameConfigManager")
@Getter
public class GameConfigManager {
	
    private static final Logger logger = LoggerFactory.getLogger(GameConfigManager.class);
    
    @Autowired
	private ApplicationContext ac;
    @Value("${spring.application.env}")
	private String applicationEnv;
    @Value("${game.api.log.open}")
    private int apiLogOpen;
    @Value("${game.country}")
    private String country;
    @Value("${spring.application.name}")
    private String appName;
    @Autowired
    private MainConfig mainConfig;
    @Autowired
    private GameConfigLoader gameConfigLoader;
    @Autowired
    private RedisService redisService;

    private Tables tables;
    

    /** 关卡ID对应的章节ID **/
    private Map<Integer, Integer> missionIdToChapterId = new HashMap<>();
    /** 最大关卡ID **/
    private int maxMissionId = 0;
    /** 最大章节ID **/
    private int maxChapterId = 0;
    /** 掉落总权重 */
    private Map<Integer, Integer> dropTotalWeight = new HashMap<>();

    private ConcurrentHashMap<Integer, Map<Integer, EquipEntity>> equipQualityMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, Map<Integer, UpdateLevelEntity>> equipLevelMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, Map<Integer, Map<Integer, Integer>>> equipLevelResourceReturnMap = new ConcurrentHashMap<>();

    // shop -> (goodsId -> GoodsEntity)
    private ConcurrentHashMap<Integer, Map<Integer, GoodsEntity>> shopGoodMap = new ConcurrentHashMap<>();

    /** 签到奖励dto */
    private ConcurrentHashMap<Integer, List<CommonProto.RewardDtoListDto>> signInRewardsDto = new ConcurrentHashMap<>();


    /**
     * 通行证完成积分
     */
    private ConcurrentHashMap<Integer, Integer> battlePassTopScoreMap = new ConcurrentHashMap<>();

    /**
     * 通行证组内最终奖励
     */
    private ConcurrentHashMap<Integer, BattlePassRewardEntity> battlePassLoopRewardMap = new ConcurrentHashMap<>();

    /**
     * 基金组索引
     */
    private ConcurrentHashMap<Integer, LevelFundEntity> groupIdLevelFundMap = new ConcurrentHashMap<>();

    /***seven day */
    private ConcurrentHashMap<Integer, List<Integer>> sevenDayTaskIds = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, Integer> sevenDayTaskStatisticsTypes = new ConcurrentHashMap<>();

    /**
     * 英雄升级总消耗 {level:{itemId:itemNum}}
     **/
    private ConcurrentHashMap<Integer, Map<Integer, Integer>> heroLevelUpTotalCost = new ConcurrentHashMap<>();
    /**
     * 英雄升阶总消耗 {advance:{itemId:itemNum}}
     **/
    private ConcurrentHashMap<Integer, Map<Integer, Integer>> heroAdvanceUpTotalCost = new ConcurrentHashMap<>();

    /** bossId -> (damage -> id) */
    private ConcurrentHashMap<Integer, Map<Long, Integer>> guildbossDamageRewards = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Integer, Map<Long, Integer>> personalBossDamageRewards = new ConcurrentHashMap<>();

    /** bossId -> entitys */
    private ConcurrentHashMap<Integer, List<RankRewardsEntity>> guildbossRewards = new ConcurrentHashMap<>();

    /** lv -> cost */
    private ConcurrentHashMap<Integer, List<List<Integer>>> guildTechLvCost = new ConcurrentHashMap<>();


    /**
     * SERVER LIST MARK
     **/
    private ConcurrentHashMap<String, ServerRangeEntity> serverListMap = new ConcurrentHashMap<>();

    /** taskType -> [taskId -> entity] */
    private ConcurrentHashMap<Integer, Map<Integer, TaskEntity>> eventTasks = new ConcurrentHashMap<>();

    /** 钓鱼 */
    private ConcurrentHashMap<Integer, List<Integer>> fishIdByFishType = new ConcurrentHashMap<>();

    /** 翻牌子 */
    private List<Integer> flipClueItemId = new ArrayList<>();


    // 加载luban生成的配置文件
    private class LuBanJsonFileLoader implements Tables.IJsonLoader {
        @Override
        public JsonElement load(String file) throws IOException {
            String content = gameConfigLoader.loadConfig(file + ".json");
            return JsonParser.parseString(content);
        }
    }

    private void loadLuBanConfig() {
        LuBanJsonFileLoader loader = new LuBanJsonFileLoader();
        try {
            tables = new Tables(loader);
        } catch (Exception e) {
            logger.error("loadLuBanConfig error", e);
        }
    }

    private void loadMainConfig() {
        try {
            String configMain = "";
            if (this.isCn()) {
                configMain = gameConfigLoader.loadConfig("Main_cn.json");
            } else {
                configMain = gameConfigLoader.loadConfig("Main.json");
            }
            this.mainConfig.setUp(configMain);
        } catch (Exception e) {
            logger.error("load MainConfig Error,", e);
        }
    }
    
    private void setContent() {
    }


    @PostConstruct
    public void initConfig() {
        loadMainConfig();
        loadLuBanConfig();
        formatConfig();
        setContent();

        String appEnv = System.getenv("APP_ENV");
        if (appEnv != null) {
            applicationEnv = appEnv;
        }
    }


    /**
     * 重新加载配置文件
     */
    public void reloadConfig(List<String> configName) throws UnknownHostException {
        gameConfigLoader.getGameConfigObjClass().forEach((k, v) -> {
            GameConfiguration<?> gameConfiguration = (GameConfiguration<?>) ac.getBean(v);
            if (configName == null) {
                gameConfiguration.reload(false);
            } else if (configName.contains(k)) {
                gameConfiguration.reload(true);
            }
        });

        loadMainConfig();
        loadLuBanConfig();
        formatConfig();

        logger.info("[{}]config reload success...", InetAddress.getLocalHost().getHostName());
    }

    private void formatConfig() {
        equipLevelResourceReturnMap.clear();
        equipLevelMap.clear();
        Map<Integer, Equip_updateLevel> updateLevel = tables.getEquip_updateLevelModel().getDataMap();
        for (Equip_updateLevel entity : updateLevel.values()) {
            if (!equipLevelResourceReturnMap.containsKey(entity.getType())) {
                equipLevelResourceReturnMap.put(entity.getType(), new HashMap<>());
            }
            Map<Integer, Map<Integer, Integer>> typeMap = equipLevelResourceReturnMap.get(entity.getType());
            if (!typeMap.containsKey(entity.getLevel())) {
                typeMap.put(entity.getLevel(), new HashMap<>());
            }
            Map<Integer, Integer> returnMap = typeMap.get(entity.getLevel());
            for (int i = 1; i < entity.getLevel(); i++) {
                for (Equip_updateLevel entity1 : updateLevel.values()) {
                    if (entity1.getType() != entity.getType()) {
                        continue;
                    }
                    if (entity1.getLevel() >= i) {
                        continue;
                    }
                    List<List<Integer>> levelupCost = entity1.getLevelupCost();
                    for (List<Integer> temp : levelupCost) {
                        int itemId = temp.get(0);
                        int itemCount = temp.get(1);
                        Integer orDefault = returnMap.getOrDefault(itemId, 0);
                        returnMap.put(itemId, orDefault + itemCount);
                    }
                }
            }
            if (!equipLevelMap.containsKey(entity.getType())) {
                equipLevelMap.put(entity.getType(), new HashMap<>());
            }
            Map<Integer, Equip_updateLevel> levelTypeMap = equipLevelMap.get(entity.getType());
            levelTypeMap.put(entity.getLevel(), entity);
        }

        equipQualityMap.clear();
        ConcurrentSkipListMap<Integer, EquipEntity> equip = getEquipConfig().getEquip();
        for (EquipEntity entity : equip.values()) {
            int id = entity.getId();
            int supId = id / 100;
            if (!equipQualityMap.containsKey(supId)) {
                equipQualityMap.put(supId, new HashMap<>());
            }
            Map<Integer, EquipEntity> qualityMap = equipQualityMap.get(supId);
            qualityMap.put(entity.getQuality(), entity);
        }

        shopGoodMap.clear();
        ConcurrentSkipListMap<Integer, GoodsEntity> goods = getIntegralShopConfig().getGoods();
        goods.forEach((k, v) -> {
            shopGoodMap.computeIfAbsent(v.getTypeId(), k1 -> new HashMap<>()).putIfAbsent(v.getID(), v);
        });

        // ---------------------------------- 签到奖励
        signInRewardsDto.clear();
        Map<Integer, SignInEntity> signInEntityMap = this.getSignInConfig().getSignIn();
        for (Map.Entry<Integer, SignInEntity> entry : signInEntityMap.entrySet()) {
            List<CommonProto.RewardDto> rewardDtos1 = CommonHelper.buildRewardDtoList(this.formateItemsConfig(entry.getValue().getDay1()));
            List<CommonProto.RewardDto> rewardDtos2 = CommonHelper.buildRewardDtoList(this.formateItemsConfig(entry.getValue().getDay2()));
            List<CommonProto.RewardDto> rewardDtos3 = CommonHelper.buildRewardDtoList(this.formateItemsConfig(entry.getValue().getDay3()));
            List<CommonProto.RewardDto> rewardDtos4 = CommonHelper.buildRewardDtoList(this.formateItemsConfig(entry.getValue().getDay4()));
            List<CommonProto.RewardDto> rewardDtos5 = CommonHelper.buildRewardDtoList(this.formateItemsConfig(entry.getValue().getDay5()));
            List<CommonProto.RewardDto> rewardDtos6 = CommonHelper.buildRewardDtoList(this.formateItemsConfig(entry.getValue().getDay6()));
            List<CommonProto.RewardDto> rewardDtos7 = CommonHelper.buildRewardDtoList(this.formateItemsConfig(entry.getValue().getDay7()));

            CommonProto.RewardDtoListDto.Builder builder1 = CommonProto.RewardDtoListDto.newBuilder();
            builder1.addAllRewardDtos(rewardDtos1);

            CommonProto.RewardDtoListDto.Builder builder2 = CommonProto.RewardDtoListDto.newBuilder();
            builder2.addAllRewardDtos(rewardDtos2);

            CommonProto.RewardDtoListDto.Builder builder3 = CommonProto.RewardDtoListDto.newBuilder();
            builder3.addAllRewardDtos(rewardDtos3);

            CommonProto.RewardDtoListDto.Builder builder4 = CommonProto.RewardDtoListDto.newBuilder();
            builder4.addAllRewardDtos(rewardDtos4);

            CommonProto.RewardDtoListDto.Builder builder5 = CommonProto.RewardDtoListDto.newBuilder();
            builder5.addAllRewardDtos(rewardDtos5);

            CommonProto.RewardDtoListDto.Builder builder6 = CommonProto.RewardDtoListDto.newBuilder();
            builder6.addAllRewardDtos(rewardDtos6);

            CommonProto.RewardDtoListDto.Builder builder7 = CommonProto.RewardDtoListDto.newBuilder();
            builder7.addAllRewardDtos(rewardDtos7);

            List<CommonProto.RewardDtoListDto> rewardDtoListDtos = new ArrayList<>();
            rewardDtoListDtos.add(builder1.build());
            rewardDtoListDtos.add(builder2.build());
            rewardDtoListDtos.add(builder3.build());
            rewardDtoListDtos.add(builder4.build());
            rewardDtoListDtos.add(builder5.build());
            rewardDtoListDtos.add(builder6.build());
            rewardDtoListDtos.add(builder7.build());
            signInRewardsDto.put(entry.getValue().getID(), rewardDtoListDtos);
        }



        // 通行证积分索引
        battlePassTopScoreMap.clear();
        battlePassLoopRewardMap.clear();
        ConcurrentSkipListMap<Integer, BattlePassRewardEntity> battlePassReward = this.getIAPConfig().getBattlePassReward();
        for (BattlePassRewardEntity entity : battlePassReward.values()) {
            if (entity.getType() == 1 || entity.getType() == 2) {
                int oldValue = battlePassTopScoreMap.getOrDefault(entity.getGroupId(), 0);
                if (entity.getScore() > oldValue) {
                    battlePassTopScoreMap.put(entity.getGroupId(), entity.getScore());
                }
            }
            if (entity.getType() == 3) {
                battlePassLoopRewardMap.put(entity.getGroupId(), entity);
            }
        }

        // 基金组索引
        groupIdLevelFundMap.clear();
        ConcurrentSkipListMap<Integer, LevelFundEntity> levelFund = getIAPConfig().getLevelFund();
        for (LevelFundEntity entity : levelFund.values()) {
            groupIdLevelFundMap.put(entity.getGroupId(), entity);
        }




        actTypeMap.clear();
        activityMap.values().forEach(v -> {
            actTypeMap.computeIfAbsent(v.getType(), k -> new ArrayList<>()).add(v);
        });

        ConcurrentSkipListMap<Integer, SevenDayTaskEntity> allTasks = this.getSevenDayConfig().getSevenDayTask();
        this.sevenDayTaskIds.clear();

        sevenDayTaskStatisticsTypes.clear();
        allTasks.forEach((k, v) -> {
            if (!this.sevenDayTaskStatisticsTypes.containsKey(v.getTaskType())) {
                this.sevenDayTaskStatisticsTypes.put(v.getTaskType(), v.getStatisticsType());
            }

            if (!this.sevenDayTaskIds.containsKey(v.getTaskType())) {
                this.sevenDayTaskIds.put(v.getTaskType(), new ArrayList<>());
            }
            this.sevenDayTaskIds.get(v.getTaskType()).add(v.getID());
        });

        // ----------------------------------- 英雄升级每级总消耗
        // ----------------------------------- 英雄每级的属性
        Map<Integer, LevelupEntity> levelUpCostTypeEntityMap = this.getGameMemberConfig().getLevelup();
        heroLevelUpTotalCost.clear();
        for (Map.Entry<Integer, LevelupEntity> entry : levelUpCostTypeEntityMap.entrySet()) {
            int level = entry.getKey();

            heroLevelUpTotalCost.computeIfAbsent(level, k -> new HashMap<>());


            Map<Integer, Integer> nowLevelCostMap = heroLevelUpTotalCost.get(level);
            for (List<Integer> config : entry.getValue().getLevelUpCost()) {
                nowLevelCostMap.put(config.get(0), config.get(1));
            }

            if (level > 1) {
                Map<Integer, Integer> costMap = heroLevelUpTotalCost.get(level - 1);
                for (Map.Entry<Integer, Integer> costMapEntry : costMap.entrySet()) {
                    int num = nowLevelCostMap.getOrDefault(costMapEntry.getKey(), 0) + costMapEntry.getValue();
                    nowLevelCostMap.put(costMapEntry.getKey(), num);
                }
            }
        }

        Map<Integer, AdvanceEntity> advanceUpCostTypeEntityMap = this.getGameMemberConfig().getAdvance();
        heroAdvanceUpTotalCost.clear();
        for (Map.Entry<Integer, AdvanceEntity> entry : advanceUpCostTypeEntityMap.entrySet()) {
            int advance = entry.getKey() - 1;

            heroAdvanceUpTotalCost.computeIfAbsent(advance, k -> new HashMap<>());


            Map<Integer, Integer> nowAdvanceCostMap = heroAdvanceUpTotalCost.get(advance);
            for (List<Integer> config : entry.getValue().getAdvanceUpCost()) {
                nowAdvanceCostMap.put(config.get(0), config.get(1));
            }

            if (advance > 0) {
                Map<Integer, Integer> costMap = heroAdvanceUpTotalCost.get(advance - 1);
                for (Map.Entry<Integer, Integer> costMapEntry : costMap.entrySet()) {
                    int num = nowAdvanceCostMap.getOrDefault(costMapEntry.getKey(), 0) + costMapEntry.getValue();
                    nowAdvanceCostMap.put(costMapEntry.getKey(), num);
                }
            }
        }

        Map<Integer, GuildBossBoxEntity> guildBossBoxMap = guildBOSSConfig.getGuildBossBox();
        guildbossDamageRewards.clear();
        guildBossBoxMap.values().forEach(v -> {
            guildbossDamageRewards.computeIfAbsent(v.getBossId(), k -> new HashMap<>()).put(v.getDamage(), v.getID());
        });

        Map<Integer, PersonalBossBoxEntity> personalBossBoxMap = getPersonalBossConfig().getPersonalBossBox();
        personalBossDamageRewards.clear();
        personalBossBoxMap.values().forEach(v -> {
            personalBossDamageRewards.computeIfAbsent(v.getBossId(), k -> new HashMap<>()).put(v.getDamage(), v.getID());
        });

        Map<Integer, RankRewardsEntity> guildBossRewardsMap = guildBOSSConfig.getRankRewards();
        guildbossRewards.clear();
        guildBossRewardsMap.values().forEach(v -> {
            guildbossRewards.computeIfAbsent(v.getBossId(), k -> new ArrayList<>()).add(v);
        });

        Map<Integer, GuildTechEntity> guildTechEntityMap = getGuildTechConfig().getGuildTech();
        guildTechLvCost.clear();
        guildTechEntityMap.values().forEach(v -> {
            guildTechLvCost.put(v.getLevel(), v.getLevelupCost());
        });

        Map<Integer, TalentEntity> talentEntityMap = getTalentSystemConfig().getTalent();
        heroTanlent.clear();
        talentEntityMap.values().forEach(v -> {
            heroTanlent.put(v.getHeroId(), v);
        });

        // 迷宫怪物分类索引
        mazeMonsterTypeMap.clear();
        MazeConfig mazeConfig = getMazeConfig();
        ConcurrentSkipListMap<Integer, com.dxx.game.config.entity.maze.MonsterEntity> mazeMonster = mazeConfig.getMonster();
        for (com.dxx.game.config.entity.maze.MonsterEntity monsterEntity : mazeMonster.values()) {
            int type = monsterEntity.getType();
            if (!mazeMonsterTypeMap.containsKey(type)) {
                mazeMonsterTypeMap.put(type, new ArrayList<>());
            }
            List<com.dxx.game.config.entity.maze.MonsterEntity> monsterEntities = mazeMonsterTypeMap.get(type);
            monsterEntities.add(monsterEntity);
        }

        serverListMap.clear();
        this.getServerRangeConfig().getServerRange().values().forEach(temp -> {
            if (temp.getMark() != null) {
                temp.getMark().forEach(tempConfig -> {
                    if (!StringUtils.isEmpty(tempConfig)) {
                        serverListMap.put(tempConfig.trim(), temp);
                    }
                });
            }
        });

        eventTasks.clear();
        this.getEventConfig().getTask().values().forEach(v -> {
            eventTasks.computeIfAbsent(v.getTaskType(), k -> new HashMap<>()).put(v.getId(), v);
        });

        // 钓鱼
        ConcurrentHashMap<Integer, List<Integer>> fishIdByAreaId = new ConcurrentHashMap<>();
        for (FishEntity entity : getEventFishingConfig().getFish().values()) {
            int type = entity.getFishType();
            List<Integer> area = fishIdByAreaId.get(type);
            if (area == null) {
                area = new ArrayList<>();
                fishIdByAreaId.put(type, area);
            }
            area.add(entity.getId());
        }
        this.fishIdByFishType = fishIdByAreaId;

        // 翻牌子
        for (Map.Entry<Integer, FlipBaseEntity> entry : this.getEventFlipCardConfig().getFlipBase().entrySet()) {
            int clueItemId = entry.getValue().getClueItem();
            if (!this.flipClueItemId.contains(clueItemId)) {
                this.flipClueItemId.add(clueItemId);
            }
        }
    }

    private List<List<Integer>> formateItemsConfig(List<List<Integer>> itemConfig) {
        List<List<Integer>> copyList = CollectionUtils.copyM(itemConfig);

        for (int i = 0; i < copyList.size(); i++) {
            if (copyList.get(i).size() < 3) {
                int itemId = copyList.get(i).get(0);
                int itemCount = copyList.get(i).get(1);
                int itemType = this.getItemConfig().getItemEntity(itemId).getItemType();

                copyList.get(i).clear();
                copyList.get(i).add(itemType);
                copyList.get(i).add(itemId);
                copyList.get(i).add(itemCount);
            }
        }
        return copyList;
    }

    public String getSensitiveWordsUrl() {
        return this.mainConfig.getSensitiveWords().get(this.country);
    }

    public String getSensitiveWordsToken() {
        return this.mainConfig.getSensitiveWords().get("token_" + this.country);
    }

    public String getApplicationEnv() {
		return applicationEnv;
	}
    /**
     * 是否是正式环境
     * @return
     */
    public boolean isProd() {
    	if (this.getApplicationEnv().equals("prod")) {
    		return true;
    	}
    	return false;
    }

    /**
     * 是否是开发环境
     */
    public boolean isDevelop() {
    	if (this.getApplicationEnv().equals("dev")) {
    		return true;
    	}
    	return false;
    }

    /**
     * 是否是准正式服环境
     * @return
     */
    public boolean isPre() {
        return this.getApplicationEnv().equals("pre");
    }

    /**
     * 是否是测试环境
     * @return
     */
    public boolean isTest() {
        if (this.getApplicationEnv().equals("test")) {
            return true;
        }
        return false;
    }

	public int getChapterId(int missionId) {
        return this.missionIdToChapterId.get(missionId);
    }

    /**
     * 是否是国内环境
     * @return
     */
    public boolean isCn() {
        return this.country.equals("cn");
    }

    public boolean isPrintApiLog() {
        return (this.apiLogOpen == 1 /*&& !isProd()*/)
                || (!this.mainConfig.getApiLogWhiteUserIds().isEmpty() && this.mainConfig.getApiLogWhiteUserIds().contains(RequestContext.getUserId()));
    }

    /**
     * 获取邮件服务器接口地址
     *
     * @return
     */
    public String getEmailServerApiUrl() {
        int idx = 0;
       if (this.isPre()) {
            idx = 1;
        } else if (this.isProd()) {
            idx = 2;
        }
        return this.getMainConfig().getEmailApiConfig().getEmail_server_api_url().get(idx);
    }

    public String getMailTempId(int testId, int prodId) {
        String testTempleId = getGameConfigConfig().getConfigEntity(testId).getValue();
        String prodTempleId = getGameConfigConfig().getConfigEntity(prodId).getValue();

        String tempId = testTempleId;
        if (this.isPre() || this.isProd()) {
            tempId = prodTempleId;
        }

        return tempId;
    }

    public String getMonthMailTempId(MonthCardEntity entity) {
        String testTempleId = entity.getPostID().get(0);
        String prodTempleId = entity.getPostID().get(1);

        String tempId = testTempleId;
        if (this.isPre() || this.isProd()) {
            tempId = prodTempleId;
        }

        return tempId;
    }

    public String getEventMailTempId(EventEntity entity) {
        String testTempleId = entity.getMailTempId().get(0);
        String prodTempleId = entity.getMailTempId().get(1);

        String tempId = testTempleId;
        if (this.isPre() || this.isProd()) {
            tempId = prodTempleId;
        }

        return tempId;
    }

    public String getEventConsumeMailTempId(EventEntity entity) {
        String testTempleId = entity.getMailRoundTempId().get(0);
        String prodTempleId = entity.getMailRoundTempId().get(1);

        String tempId = testTempleId;
        if (this.isPre() || this.isProd()) {
            tempId = prodTempleId;
        }

        return tempId;
    }

    public String getFishMail(FishingBaseEntity entity) {
        String testTempleId = entity.getMailItemsId().get(0);
        String prodTempleId = entity.getMailItemsId().get(1);

        String tempId = testTempleId;
        if (this.isPre() || this.isProd()) {
            tempId = prodTempleId;
        }

        return tempId;
    }

    public String getFlipMail(FlipBaseEntity entity) {
        String testTempleId = entity.getMailItemsId().get(0);
        String prodTempleId = entity.getMailItemsId().get(1);

        String tempId = testTempleId;
        if (this.isPre() || this.isProd()) {
            tempId = prodTempleId;
        }

        return tempId;
    }

    public String getDiveMail(DiveBaseEntity entity) {
        String testTempleId = entity.getMailItemsId().get(0);
        String prodTempleId = entity.getMailItemsId().get(1);

        String tempId = testTempleId;
        if (this.isPre() || this.isProd()) {
            tempId = prodTempleId;
        }

        return tempId;
    }

}















